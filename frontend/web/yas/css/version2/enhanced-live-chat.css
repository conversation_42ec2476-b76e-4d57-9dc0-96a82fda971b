/* Enhanced Live Chat Widget - Clean Reset */

#enhanced-chat-widget {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    margin-bottom: 20px 0px;
    overflow: hidden;
    width: 100%;
    /* max-width: 600px; */
    height: auto;
}

/* Chat Header Enhanced */
.chat-header-enhanced {
    align-items: center;
    padding: 24px 24px 16px;
    display: flex;
    flex-direction: row;
    background: #0B66C2;
    border-bottom: 1px solid #E2E8F0;
    border-radius: 8px;
    color: #fff !important;
}

.header-top-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 0px 0px 12px;
    width: 225px;
    height: 84px;
    flex: none;
    order: 0;
    flex-grow: 0;
    gap: 4px;
}

.header-left h4 {
    font-size: 18px;
    line-height: 28px;
    align-items: center;
    letter-spacing: -0.45px;
    font-weight: 700;
}

.live-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 8px;
}

.live-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ff4444;
    animation: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
}

.live-text {
    font-size: 10px;
    font-weight: 700;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    color: #fff;
}

.chat-title {
    font-size: 18px;
    font-weight: 700;
    margin: 0;
    color: #fff;
    line-height: 1.2;
}

.chat-subtitle {
    font-size: 12px;
    color: #fff;
    font-weight: 400;
    margin: 0;
}

.chat-stats {
    display: flex;
    align-items: center;
    padding: 2px 0px 0px;
    width: 100%;
    gap: 138px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #fff;
}

.stat-icon {
    width: 18px;
    height: 18px;
}

.stat-number {
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
}

.active-number {
    font-size: 16px;
    font-weight: 700;
    color: #4ade80;
}

.stat-label {
    font-weight: 400;
    font-size: 18px;
    line-height: 22px;
    color: rgba(255, 255, 255, 0.8);
}

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}

/* Chat Content */
.chat-content {
    background: #fff;
    padding: 10px 30px;
}

/* Lead Form Section */
.chat-lead-form {
    padding: 0;
}

/* Horizontal Layout */
.chat-horizontal-layout {
    display: flex;
    gap: 17px;
    align-items: flex-start;
}

.recent-messages-section {
    min-width: 0;
    padding: 10px;
}

.otp-form-section {
    flex: 1;
    min-width: 0;
}

.recent-messages-section h5,
.recent-messages-preview h5 {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #020817;
}

.recent-messages-preview {
    margin-bottom: 20px;
}

.message-preview {
    border-radius: 8px;
    padding: 8px 0px;
}

.preview-item {
    border: 1px solid #e2e8f0;
    margin-bottom: 8px;
    padding: 10px 14px;
    background: #F3F7FC;
    border-radius: 4px;
}

.preview-item:last-child {
    margin-bottom: 0;
}

.preview-item strong {
    font-size: 12px;
    color: #333;
    font-weight: 400;
}

.preview-item .time {
    font-size: 11px;
    color: #999;
    float: right;
}

.preview-item p {
    font-size: 12px;
    color: #666;
    margin: 4px 0 0 0 !important;
    clear: both;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.join-form-section {
    text-align: center;
    margin-top: 8px;
    padding: 28px 24px !important;
    height: 291px;
    border-radius: 8px;
    background: linear-gradient(180.11deg, rgba(11, 102, 194, 0.04) 0.1%, rgba(11, 102, 194, 0.25) 22.86%, #0B66C2 99.9%);
    box-shadow: inset 0 0 0 1px rgba(11, 102, 194, 0.2);
}

.join-chat-button {
    margin-bottom: 12px;
}

.btn-join-chat {
    align-items: center;
    padding: 4px 2px;
    width: 75px;
    height: 22px;
    background: #3C83F6;
    border-radius: 20px;
    color: #fff;
    font-size: 12px;
    font-weight: 600;
    transition: background 0.3s;
}

.students-count {
    font-size: 12px;
    margin-bottom: 16px !important;
    color: #666;
    text-align: center;
}

.students-count strong {
    color: #000;
    font-weight: 600;
}

.lead-form {
    text-align: left;
}

.lead-form .form-control:focus {
    border-color: #3c83f6;
    outline: none;
    box-shadow: 0 0 0 2px rgba(60, 131, 246, 0.2);
}

.join-chat-submit {
    color: white;
    border: none;
    font-weight: 600;
    transition: background 0.3s;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 10px;
    height: 40px;
    background: #ff4e53;
    border-radius: 6px;
    font-weight: 600;
    font-size: 12px;
    line-height: 14px;
}

/* Chat Interface */
.chat-interface {
    display: none;
    padding: 16px;
    background: #fff;
}

.chat-messages-area {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    background: #f8f9fa;
}

.welcome-message {
    text-align: center;
    padding: 12px;
    background: rgba(66, 133, 244, 0.1);
    border-radius: 8px;
    margin-bottom: 16px;
}

.user-joined {
    color: #4285f4;
    font-weight: 500;
}

.chat-message {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    margin-bottom: 16px;
    width: 100%;
}

.message-footer {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: #888;
    margin-top: 4px;
    justify-content: flex-start !important;
}

.message-footer.current-user-live {
    justify-content: flex-end !important;
}

.message-footer .timestamp {
    display: flex;
    /* keep icon + text inline */
    align-items: center;
    gap: 4px;
}

.highlighted .common-text-box {
    background: #3c83f6;
    color: #fff;
}

.highlighted .chat-user-link,
.highlighted .chat-attachment-link {
    color: #fff !important;
}

.message-footer .tick-icon svg {
    stroke: #3ae478 !important;
}

.message-footer svg {
    width: 12px;
    height: 12px;
    stroke: #888;
}

.message-footer .timestamp p {
    margin: 0;
    /* remove default p spacing */
}

.message-author {
    font-weight: 600;
    font-size: 13px;
    color: #333;
}

.message-role {
    background: #ff9800;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    text-transform: uppercase;
}

.message-time {
    font-size: 11px;
    color: #999;
    margin-left: auto;
}

.message-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
}

.message-bubble {
    border-radius: 12px;
    padding: 3px 12px;
    max-width: 80%;
    word-wrap: break-word;
    margin-left: 20px;
    margin-bottom: 4px;
}

.message-text {
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    margin: 0;
}

.message-timestamp {
    font-size: 11px;
    color: #888;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 0px;
    gap: 5.13px;
    margin-top: 4px;
    text-align: left;
    width: auto;
}

.attachment {
    display: flex;
    align-items: flex-start;
    /* icon aligns top if filename wraps */
    gap: 6px;
    max-width: 250px;
    /* adjust width as per chat bubble */
}

.highlighted .chat-attachment-link {
    color: #fff;
}

.chat-attachment-link {
    display: inline-flex;
    flex-direction: column;
    /* ensures filename wraps neatly */
    color: #0073e6;
    text-decoration: none;
}

.chat-attachment-link-icon {
    flex-shrink: 0;
    /* keep icon size fixed */
    font-size: 16px;
}

.filename,
.message-text {
    white-space: normal;
    /* ✅ allow wrapping */
    word-break: break-word;
    /* ✅ break very long strings */
    overflow: visible;
    /* ✅ no truncation */
}

.chat-input-area {
    border-top: 1px solid #eee;
    background: white;
}

.chat-tabs {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 10px;
    width: 162px;
    height: 40px;
    background: #FFFFFF;
    box-shadow: 0px 4px 11px rgba(0, 0, 0, 0.07);
    border-radius: 6px;
}

.chat-tabs-container .tab {
    padding: 10px 5px;
    font-size: 12px;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    height: 30px;
    margin-top: 6px;
    margin-bottom: 6px;
}

.chat-tabs-container .tab.active {
    color: var(--primary-font-color);
}

.chat-tabs-container .tab.active .tab-label-active {
    font-weight: 600;
    width: 72px;
    height: 20px;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #020817;
}

.chat-tabs-container .chat-tabs .tab svg {
    display: flex;
    padding: 0px 5px 0px 0px;
    width: 18.5px;
    height: 20px;
}

.send-btn {
    background: #4285f4;
    color: #fff;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 15%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s;
    right: -19px;
    position: relative;
}

.send-btn:hover {
    background: #3367d6;
}

/* Chat Guidelines Modal */
.chatGuidelinesModal {
    height: 678px;
    display: grid;
}

.chat-guidelines-modal {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.chat-guidelines-modal .modal-header {
    text-align: center;
    border-bottom: none;
    padding: 24px 24px 0;
    position: relative;
}

.chat-guidelines-modal .close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 24px;
    opacity: 0.5;
    border: none;
    background: none;
    cursor: pointer;
}

.chat-guidelines-modal .close:hover {
    opacity: 0.8;
}

.guidelines-icon {
    background: #4285f4;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    font-size: 24px;
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
}

.chat-guidelines-modal h2 {
    color: #3c83f6;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 20px;
    line-height: 28px;
}

.chat-guidelines-modal p.guidelines-subtitle-p {
    color: #64748b;
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 35px;
}

.chat-guidelines-modal .modal-body {
    padding: 0 24px 24px;
}

.guideline-item {
    display: flex;
    align-items: flex-start;
    gap: 17px;
    margin-bottom: 25px;
}

.guideline-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    flex-shrink: 0;
    margin-top: 2px;
}

.guideline-icon.success, .guideline-icon.warning, .guideline-icon.info {
    background: #3ae4781a;
    color: white;
}

.guideline-content .guideline-title {
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: #333;
    line-height: 21px;
}

.guideline-content .guideline-description {
    font-size: 12px;
    color: #666;
    margin: 0;
    line-height: 1.4;
}

.safe-space-section {
    background: #f3f4f680;
    padding: 16px;
    border-radius: 8px;
    margin-top: 20px;
    margin-bottom: 24px;
}

.safe-space-icon {
    color: #2196f3;
    font-size: 20px;
    margin-top: 2px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    justify-content: normal;
}

.safe-space-title {
    font-size: .875rem;
    line-height: 1.25rem;
    font-weight: 600;
    margin-right: 8px;
    margin-left: 8px;
}

.safe-space-content {
    font-size: 12px;
    color: #64748b;
    margin: 0;
    line-height: 16px;
}

.chat-guidelines-modal .modal-footer {
    border-top: none;
    padding: 0 24px 24px;
}

#start-chatting-btn {
    background: linear-gradient(135deg, #3F83F8, #1D4ED8);
    border: none;
    padding: 12px;
    border-radius: 8px;
    font-weight: 600;
    color: white;
    width: 100%;
    font-size: 14px;
}

#start-chatting-btn:hover {
    background: #3c83f6e6;
}

/* CSS Reset for Widget */
#enhanced-chat-widget * {
    box-sizing: border-box;
}

#enhanced-chat-widget h1, #enhanced-chat-widget h2, #enhanced-chat-widget h3,
#enhanced-chat-widget h4, #enhanced-chat-widget h5, #enhanced-chat-widget h6 {
    margin: 0;
    padding: 0;
}

#enhanced-chat-widget p {
    margin: 0;
    padding: 0;
}

/* #enhanced-chat-widget ul, #enhanced-chat-widget li {
    margin: 0;
    padding: 0;
    list-style: none;
} */

#enhanced-chat-widget button {
    border: none;
    cursor: pointer;
}

#enhanced-chat-widget button:disabled {
    opacity: 0.8;
    cursor: not-allowed;
    align-items: center;
    padding: 10px;
    width: 100%;
    height: 40px;
    background: #ff4e53;
    border-radius: 6px;
    margin-top: 0px;
    font-weight: 600;
    font-size: 12px;
    line-height: 14px;
}

#join-chat-form-btn-submit svg {
    margin-left: 5px;
}

/* Input text color on focus and when has value */
.form-group-live-chat input:focus,
.form-group-live-chat input:not(:placeholder-shown),
input#otp-input-live-chat:not(:placeholder-shown) {
    color: #282828 !important;
}

/* SVG icon color on focus and when input has value */
.form-group-live-chat input:focus+svg,
.form-group-live-chat input:not(:placeholder-shown)+svg {
    fill: #282828 !important;
}

.form-group-live-chat input:focus+svg path,
.form-group-live-chat input:not(:placeholder-shown)+svg path {
    fill: #282828 !important;
}

/* Mobile number dial code color */
.form-group-live-chat.mobileNumberCode:focus-within .dialCode,
.form-group-live-chat.mobileNumberCode input:not(:placeholder-shown)~.dialCodeDiv .dialCode {
    color: #282828 !important;
}

/* Specific targeting for individual input fields */
#lead-name:focus,
#lead-name:not(:placeholder-shown) {
    color: #282828 !important;
}

#lead-name:focus+svg path,
#lead-name:not(:placeholder-shown)+svg path {
    fill: #282828 !important;
}

/* Fix for email input - reset to default first then apply styles */
#lead-email {
    color: rgb(217, 217, 217) !important;
}

#lead-email:focus,
#lead-email:not(:placeholder-shown) {
    color: #282828 !important;
}

#lead-email:focus+svg path,
#lead-email:not(:placeholder-shown)+svg path {
    fill: #282828 !important;
}

#lead-mobile:focus,
#lead-mobile:not(:placeholder-shown) {
    color: #282828 !important;
}

/* Smooth transitions */
.form-group-live-chat svg,
.form-group-live-chat svg path {
    transition: fill 0.3s ease;
}

.form-group-live-chat .dialCode {
    transition: color 0.3s ease;
}

.form-group-live-chat input {
    transition: color 0.3s ease;
}

#enhanced-chat-widget input, #enhanced-chat-widget select {
    outline: none;
    box-sizing: border-box;
    padding: 12px 11px;
    width: 297px;
    height: 40px;
    padding-left: 40px;
    font-weight: 300;
    font-size: 12px;
    line-height: 14px;
    color: #838383;
    background: #FFFFFF;
    border: 0.5px solid #E2E8F0;
    border-radius: 6px;
    display: block;
}

#enhanced-chat-widget .form-group-live-chat svg {
    position: relative;
    left: 15px;
    top: -27px;
}

#enhanced-chat-widget .mobileNumberCode {
    display: flex;
    width: 297px;
}

#enhanced-chat-widget .mobileNumberCode input {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
    padding: 10px;
}

.chat-lead-form-inputs {
    display: flex;
    flex-direction: column;
}

#enhanced-chat-widget .dialCodeDiv img {
    position: absolute;
    width: 19.64px;
    height: 19.64px;
    left: 12.43px;
    top: 9.18px;
    transform: matrix(1, 0, 0, -1, 0, 0);
}

#enhanced-chat-widget .dialCodeDiv {
    background: #FFFFFF;
    justify-content: flex-end;
    border-radius: 6px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
    min-width: 80px;
}

#enhanced-chat-widget .dialCode {
    position: relative;
    right: 15px;
    font-size: 12px;
}

#enhanced-chat-widget .form-group-live-chat {
    height: 53px;
}

#enhanced-chat-widget .errorMsgEmailLive {
    font-size: 11px;
    color: red;
    margin-bottom: 5px;
    position: relative;
    right: -6px;
    top: -18px;
}

.live-chat-messages {
    height: 500px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.welcome-notification {
    background: rgba(115, 224, 132, 0.2);
    padding: 10px;
    text-align: center;
    font-size: 12px;
    color: #3ae478;
    border-bottom: 1px solid #e0e0e0;
    width: 100%;
    height: 27px;
}

.welcome-text-user-names {
    font-size: 14px;
    line-height: 17px;
    color: #73E084;
    position: relative;
    top: -4px;
}

.welcome-notification i {
    margin-right: 5px;
}

.chat-tabs-container {
    background: #fff;
}

.tab .badge {
    background: #ff4444;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 600;
    position: relative;
    right: 11px;
    margin-bottom: 13px;
    width: 20px;
    height: 20px;
    display: inline-flex;
    justify-content: center;
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    background: #fff;
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding-top: 20px;
    border-radius: 8px;
    max-height: 400px;
    overflow-y: auto;
    overflow-x: hidden;
}

.messages-container::-webkit-scrollbar {
    display: none;
}

.message {
    display: flex;
    margin-bottom: 16px;
    align-items: flex-start;
}

.moderator-avatar {
    background: #ff6b35;
}

.user-avatar-pk {
    background: #e91e63;
}

.user-avatar-rm {
    background: #4caf50;
}

.user-avatar-sp {
    background: #9c27b0;
}

.user-avatar-as {
    background: #ff9800;
}

.mod-badge {
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    font-weight: 600;
    width: 43px;
    height: 18px;
    text-align: center;
    background: #FEEA36;
    border-radius: 25px;
}

/* .timestamp {
    font-size: 11px;
    color: #999;
    margin-left: auto;
} */

/* .moderator-text {
    background: #3ae478;
    color: white;
    padding: 8px 12px;
    border-radius: 12px;
    display: inline-block;
} */

.highlighted {
    justify-content: flex-end;
    align-items: flex-end;
}

.highlighted .message-content {
    align-items: flex-end;
    max-width: 80%;
}

.highlighted .message-bubble {
    margin-left: 0;
    margin-right: 0;
}

.highlighted .message-timestamp {
    justify-content: flex-end;
    text-align: right;
}

.message-status {
    color: #4285f4;
    font-size: 12px;
}

.message-input-container {
    background: #fff;
    border-top: 1px solid #e0e0e0;
    display: flex;
    padding-left: 0px !important;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
}

.input-wrapper {
    display: flex;
    align-items: center;
    /* background: #f5f5f5; */
    border-radius: 20px;
    padding: 4px;
    width: 100%;
}

.input-wrapper .attachment-btn {
    border: none;
    color: #000;
    padding: 5px;
    cursor: pointer;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
}

/* Chat Guidelines Modal */
.modal.fade {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1050;
    outline: 0;
}

.modal.fade.show {
    display: flex !important;
    align-items: center;
    justify-content: center;
}

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1040;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 0.5rem;
    max-width: 500px;
    height: 678px;
    padding: 24px;
    display: flex;
    flex-direction: column;
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0.3rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.5);
}

#enhanced-chat-widget .select2-container--default .select2-selection--single {
    border: none !important;
}

#enhanced-chat-widget .select2-container {
    display: inline !important;
    top: -3px;
}

#enhanced-chat-widget .form-control.inputStreamLiveContainer.modalInputContainer.streamLiveClass.streamLiveCategory.inputStreamLive,
#enhanced-chat-widget .form-control.inputLevelLiveContainer.modalInputContainer.levelLiveClass.levelLiveCategory.inputLevelLive {
    height: 40px;
    width: 314px;
    background: #fff;
}

#enhanced-chat-widget .select2-container .select2-selection--single .select2-selection__rendered {
    padding-left: 0 !important;
}

.stream-dropdown.select2-dropdown--below,
.level-dropdown.select2-dropdown--below {
    left: -11px !important;
    top: -6px !important;
}

#enhanced-chat-widget .select2-container:focus-visible {
    border: none !important;
    outline: none !important;
}

.chat-stats .stat-item .usersIcon {
    background-position: 1149px 637px;
    width: 20px;
    height: 20px;
    display: inline-block;
}

.chat-stats .stat-item .statIcon {
    background-position: -676px -1137px;
    width: 20px;
    height: 20px;
    display: inline-block;
}

.otp-box {
    border-radius: 12px;
    padding: 0px;
    text-align: center;
    margin: 0;
    width: 100%;
    box-sizing: border-box;
}

.otp-box .join-chat-button {
    margin-bottom: 12px;
}

.otp-box .students-count {
    font-size: 12px;
    margin-bottom: 16px !important;
    color: #666;
}

.otp-box .students-count strong {
    color: #000;
    font-weight: 600;
}

.otp-box span.otplabel {
    color: #FFFFFF;
    font-size: 13.4531px;
    display: block;
    margin: 0px 0px;
    font-weight: 400;
    font-size: 13.4531px;
    line-height: 16px;
    align-items: center;
}

.otp-box input[type="text"] {
    width: 100%;
    height: auto;
    font-weight: 300;
    font-size: 12px;
    line-height: 14px;
    margin: 12px 0;
    border: 1px solid #ddd;
    border-radius: 8px;
    text-align: center;
    box-sizing: border-box;
    padding-left: 0px !important;
}

.otp-box .verify-btn {
    width: 100%;
    color: #fff;
    border: none;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    margin: 8px 0;
    transition: background 0.3s;
    height: 37px;
    background: #ff4e53;
    border-radius: 6px;
    margin-top: 10px !important;
}

.otp-box .verify-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
}

.otp-box .change-number {
    display: block;
    color: #fff;
    text-decoration: none;
    font-size: 14px;
    margin-top: 12px;
    cursor: pointer;
}

.otp-box .change-number:hover {
    text-decoration: underline;
    background-color: #ff4e53;
}

.otp-toast {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    font-size: 14px;
}

.otp-toast strong {
    display: block;
    margin-bottom: 4px;
}

/* #enhanced-chat-widget #verify-btn-live-chat:disabled {
    background: #ff4e53;
    opacity: 0.8;
} */

#chat-content .errorMsgOtpLive {
    color: red;
    font-size: 12px;
}

.guidelinesClose {
    position: absolute;
    right: 10px;
    top: 9px;
    background: none;
    border: none;
}

.emoji-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    cursor: pointer;
    color: #000;
    font-size: 16px;
}

.chat-icon .fa-comment-o {

    width: 50px;
    height: 50px;
    background: #fff3;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 22px;
}

.attachment-btn:hover {
    background: #3ae478;
    /* green background */
    color: #fff;
    /* make icon white */
    border-radius: 15%;
    /* optional: make it circular on hover */
    transition: background 0.3s, color 0.3s;
}

.emoji-btn:hover {
    height: 20px;
    top: 50%;
    background: #3ae478;
    color: #fff;
    transition: background 0.3s, color 0.3s;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 13%;
}

/* Input field + emoji wrapper */
.input-with-emoji {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    gap: 4px;
    right: -10px;
}

.message-input {
    flex: 1;
    padding: 10px 12px;
    border-radius: 8px;
    border: 1px solid #ccc;
    font-size: 14px;
    min-width: 0;
    height: 40px;
}

.moderator-text a, .moderator-text .message-text {
    color: #fff !important;
}

.moderator-text .attachment, .moderator-text .message-text {
    background: none !important;
}

#input-hint-text {
    font-size: 10px;
    color: #767676;
    text-align: justify;
    margin-left: 52px !important;
    position: relative;
    top: -12px;
}

.moderator-text ol {
    margin: 0;
    padding: 0;
    padding-left: 20px;
}

.moderator-text ol li {
    margin: 0;
    padding: 0;
}

/* Additional styles for exact Figma match */
.chat-interface {
    background: #fff;
    border-radius: 0 0 16px 16px;
}

.message-input-container {
    background: #fff;
    border-top: 1px solid #e5e7eb;
    padding: 16px;
    border-radius: 0 0 16px 16px;
}

.input-wrapper {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 24px;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.message-input {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 20px;
    outline: none;
    flex: 1;
    font-size: 14px;
    color: #374151;
    padding: 10px 16px;
}

.message-input::placeholder {
    color: #9ca3af;
}

.send-btn {
    background: #3c83f6;
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background 0.2s;
    margin-left: 8px;
}

.send-btn:hover {
    background: #2563eb;
}

.send-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
}

/* Message styling improvements */
.message {
    margin-bottom: 16px;
    display: flex;
    gap: 12px;
}

.message-avatar {
    display: flex;
    font-size: 12px;
    font-weight: 600;
    color: #fff;
    flex-direction: column;
    padding: 10px 10px;
    width: 36px;
    height: 36px;
    background: #FF4E53;
    border-radius: 100px;
}

.message-input-section {
    display: flex;
    padding: 12px 16px;
    gap: 8px;
    align-items: center;
}

.message-input:focus {
    border-color: #4285f4;
}

.message-avatar-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0px;
    gap: 10px;
    margin-bottom: 4px;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.message-header .username {
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    color: #020817;
}

.message-text {
    /* background: #f3f4f6; */
}

.common-text-box {
    padding: 10px 14px;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.4;
    color: #374151;
    word-wrap: break-word;
    background: #f3f4f6;
    margin: 0;
}

.moderator-text {
    background: #73E084;
}

.message-footer {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: 4px;
}

.timestamp {
    font-size: 11px;
    color: #9ca3af;
    display: flex;
    align-items: center;
    gap: 2px;
}

/* Highlighted messages (current user) */
.highlighted {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: flex-end;
    padding: 0px;
    gap: 10px;
    width: 100%;
}

.highlighted .message-content {
    align-items: flex-end;
    max-width: 80%;
}

.highlighted .common-text-box {
    background: #3c83f6;
    color: #fff;
}

.highlighted .message-bubble {
    margin-left: 0;
    margin-right: 0;
}

.highlighted .message-timestamp {
    justify-content: flex-end;
    text-align: right;
}

.highlighted .message-footer {
    justify-content: flex-end;
}

/* Mobile-specific flexible height adjustments */
@media (max-width: 768px) {
    #enhanced-chat-widget {
        max-width: 100%;
        margin: 0 16px 20px 16px;
        border-radius: 12px;
    }

    .chat-header-enhanced {
        padding: 16px;
    }

    .chat-title {
        font-size: 18px;
    }

    .chat-stats {
        padding: 0 10px;
        gap: 20px;
    }

    .stat-item {
        flex-direction: column;
        gap: 4px;
        text-align: center;
    }

    .stat-number, .active-number {
        font-size: 16px;
    }

    .stat-label {
        font-size: 10px;
        margin-left: 0;
    }

    .chat-content {
        padding: 16px;
    }

    .chat-horizontal-layout {
        flex-direction: column;
        gap: 16px;
    }

    .join-form-section {
        padding: 16px;
        margin-top: 0;
    }

    .lead-form .form-control {
        height: 48px;
        font-size: 16px;
        margin-bottom: 12px;
    }

    .join-chat-submit {
        height: 48px;
        font-size: 16px;
    }

    .otp-box {
        margin: 16px 0;
        padding: 16px;
    }

    .otp-box input[type="text"] {
        height: 48px;
        font-size: 16px;
    }

    .otp-box .verify-btn {
        height: 48px;
        font-size: 16px;
    }

    .modal-dialog {
        margin: 16px;
        max-width: calc(100% - 32px);
    }

    .preview-item {
        padding: 8px;
        margin-bottom: 6px;
    }

    .message-preview {
        padding: 8px;
        margin-bottom: 12px;
    }
}