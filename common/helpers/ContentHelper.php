<?php

namespace common\helpers;

use Carbon\Carbon;
use common\models\Exam;
use common\models\ExamContent;
use common\services\UserService;
use Yii;
use DOMDocument;
use common\models\ExamDate;
use common\models\LeadBucket;
use common\models\LeadBucketTagging;
use common\models\MediaDrive;
use common\models\MediaDriveUploadType;
use frontend\helpers\Url;
use yii\db\Query;
use yii\helpers\BaseStringHelper;
use yii\helpers\StringHelper;
use yii\helpers\Inflector;
use frontend\helpers\Html;

class ContentHelper
{
    const EXAM_YEAR = '2023';
    const COURSE_ADMISSION_YEAR = '2025';

    /**
     * Get current year
     *
     * @return string Current year in YYYY format
     */
    public static function getCurrentYear()
    {
        return date('Y');
    }

    public static $_hashTagLists = [
        '#application_from_date' => 'application-form.start',
        '#year' => self::EXAM_YEAR,
        '#application_to_date' => 'application-form.end',
        '#admit_card_date' => 'admit-card.start',
        '#result_date' => 'result-date.start',
        '#from_date' => 'exam-start.start',
        '#counselling_date' => 'counselling-date.start',
        '#to_date' => 'exam-start.end',
        '#notification_date' => 'notification-date.start',
        '#application_form_correction_start_date' => 'application-form-correction.start',
        '#application_form_correction_last_date' => 'application-form-correction.end',
        '#admit_card_end_date' => 'admit-card.end',
        '#provisional_answer_key_date' => 'provisional-answer-key-date.start',
        '#final_answer_key_date' => 'final-answer-key-date.start',
        '#counselling_round_1_registration_start_date' => 'round-one-counselling-registration.start',
        '#counselling_round_1_registration_end_date' => 'round-one-counselling-registration.end',
        '#counselling_round_1_choice_filling_start_date' => 'round-one-counselling-choice-filling.start',
        '#counselling_round_1_choice_filling_end_date' => 'round-one-counselling-choice-filling.end',
        '#counselling_round_1_seat_allotment_date' => 'round-one-counselling-seat-allotment-list.start',
        '#counselling_round_1_reporting_date' => 'round-one-counselling-date-of-reporting.start',
        '#counselling_round_2_registration_start_date' => 'round-two-counselling-registration.start',
        '#counselling_round_2_registration_end_date' => 'round-two-counselling-registration.end',
        '#counselling_round_2_choice_filling_start_date' => 'round-two-counselling-choice-filling.start',
        '#counselling_round_2_choice_filling_end_date' => 'round-two-counselling-choice-filling.end',
        '#counselling_round_2_seat_allotment_date' => 'round-two-counselling-seat-allotment-list.start',
        '#counselling_round_2_reporting_date' => 'round-two-counselling-date-of-reporting.start',
        '#counselling_round_3_registration_start_date' => 'round-three-counselling-registration.start',
        '#counselling_round_3_registration_end_date' => 'round-three-counselling-registration.end',
        '#counselling_round_3_choice_filling_start_date' => 'round-three-counselling-choice-filling.start',
        '#counselling_round_3_choice_filling_end_date' => 'round-three-counselling-choice-filling.end',
        '#counselling_round_3_seat_allotment_date' => 'round-three-counselling-seat-allotment-list.start',
        '#counselling_round_3_reporting_date' => 'round-three-counselling-date-of-reporting.start',
        '#interview_date' => 'interview-date.start',
        '#portfolio_review_date' => 'portfolio-review-and-personal-interaction-round.start',
        '#merit_list_date' => 'declaration-of-merit-list.start'
    ];
    public static $_coursePageContent = [
        'about',
        //  'syllabus-subjects',
        // 'jobs-scope-salary',
        //'admission'
    ];

    public static $_collegePageContent = [
        'info',
        'courses-fees',
        'admission',
        'cut-off',
        'facilities',
        'result',
        'ranking',
        'placements',
        'scholarships'
    ];
    /**
     * Parse content to replace hash tags
     *
     * @param $dates collection
     * @param $content string
     * @return string
     */
    public static function parseExamContent(string $content, $dates = null, $year = self::EXAM_YEAR): string
    {
        $data = [];
        foreach (self::$_hashTagLists as $key => $value) {
            if ($key == '#year') {
                $data[$key] = $year;
                continue;
            }
            list($slug, $attr) = explode('.', $value);

            if (isset($dates)) {
                foreach ($dates as $date) {
                    if ($date['slug'] == $slug) {
                        if ($date['type'] == ExamDate::TYPE_TENTATIVE) {
                            $data[$key] =  self::getDateWeek($date[$attr]) . ' (Tentative)';
                        } else {
                            $data[$key] =  \Yii::$app->formatter->format(substr($date[$attr], 0, 10), 'date');
                        }
                    }
                }
            }
        }

        return strtr(html_entity_decode($content), $data);
    }

    /**
     * Getting Date in Week Format
     *
     * @param $dateFormat, Exam Tentative Date
     *
     * @return string | Tentative Date in week format 2022-03-12(2nd week of march)
     */
    public static function getDateWeek($dateFormat)
    {
        $data = [];

        $date = Carbon::parse($dateFormat);
        $weekNumber = $date->weekOfMonth;

        if ($weekNumber) {
            $ordinal = $weekNumber . date('S', mktime(0, 0, 0, 0, $weekNumber, 0));
        }

        $data['month'] = $date->format('F');
        $data['year'] = $date->year;

        return (($ordinal ?? '') . ' week of ' . ($data['month'] ?? '') . ' ' . ($data['year'] ?? ''));
    }

    public static function htmlDecode($content, $stripTags = false)
    {
        $content = html_entity_decode($content);

        if ($stripTags) {
            $content = strip_tags($content);
        }

        return $content;
    }

    public static function removeStyleTag($html)
    {
        $data = preg_replace('/\sstyle=("|\').*?("|\')/i', '', $html);

        $content = str_replace(['<html>', '</html>', '<body>', '</body>'], '', $data);

        return $content;
    }

    public static function parseMetaTags(string $tags)
    {
        $data = [];
        $dom = new DOMDocument();
        $dom->loadHTML($tags);
        $metas = $dom->getElementsByTagName('meta');

        $title = $dom->getElementsByTagName('title');
        if ($title->length > 0) {
            $data['title'] = $title->item(0)->textContent;
        }

        for ($i = 0; $i < $metas->length; $i++) {
            $meta = $metas->item($i);
            if ($meta->getAttribute('name') == 'description') {
                $data['description'] = $meta->getAttribute('content');
            }
        }

        return $data;
    }

    public static function getBoardSeoInfo($board_name, $state_id, $page, $type = '', $subject = '')
    {
        $excludedStates = [1, 3, 4, 5, 7, 8, 10, 19, 20, 22, 23, 25, 27, 29, 37];

        if (in_array($state_id, $excludedStates)) {
            return [];
        }

        $boardName = preg_replace('/\s*(10th|12th)$/i', '', $board_name);

        if ($type == 'supplementary') {
            $seoKey = ($type) ? 'subject-wise-' . $type . '-' . $page : $type;
        } else {
            $seoKey = ($type) ? 'subject-wise-' . $type : $page;
        }

        $seoInfo = DataHelper::$boardDefaultSeoInfo[$seoKey] ?? [];

        $formattedType = $type ? ucwords(str_replace('-', ' ', $page)) : '';

        if (empty($seoInfo)) {
            return [];
        }


        $data = [];

        foreach ($seoInfo as $key => $value) {
            $data[$key] = strtr($value, [
                '{board-name}' => $boardName,
                '{subpage-name}' => $formattedType, //subpage
                '{subject}' => ucfirst($page),
                '{year}' => self::getCurrentYear()
            ]);
        }

        return $data;
    }


    public static function getExamDefaultSeoInfo($examName, $page, $dates, $year = self::EXAM_YEAR, $type = null, $parentPage = null)
    {
        if (in_array($page, DataHelper::$examSeoSubPages) || $type != null) {
            return self::getExamDefaultSubPagesSeoInfo($examName, $page, $dates, $year = self::EXAM_YEAR, $type);
        }

        if (preg_match('/^\d{4}$/', $page)) {
            if (isset(DataHelper::$examDefaultSeoInfo['{year}'])) {
                $seoInfo = DataHelper::$examDefaultSeoInfo['{year}'];
                array_walk_recursive($seoInfo, function (&$value) use ($page) {
                    $value = str_replace('{year}', $page, $value);
                });
            } else {
                $seoInfo = [];
            }
        } else if ($parentPage != null) {
            $textName = $parentPage . '-dropdown';
            $seoInfo = DataHelper::$examDefaultSeoInfo[$textName] ?? [];
        } else {
            $seoInfo = DataHelper::$examDefaultSeoInfo[$page] ?? [];
        }

        if (empty($seoInfo)) {
            return [];
        }

        foreach ($dates as $date) {
            if ($date->slug == 'exam-start') {
                $year = $year;
            }
        }
        $data = [];

        foreach ($seoInfo as $key => $value) {
            $data[$key] = strtr($value, [
                '{exam-name}' => $examName,
                '{year}' => $year,
                '{subject}' => ucfirst($page),
                '{current_year}' => self::getCurrentYear()
            ]);
        }
        return $data;
    }

    private function getExamDefaultSubPagesSeoInfo($examName, $page, $dates, $year = self::EXAM_YEAR, $type = null)
    {
        if ($type != null) {
            if (in_array($page, DataHelper::$examSeoSubPages)) {
                $seoInfo = DataHelper::$examDefaultSeoInfo[$page] ?? [];
            } else {
                $seoInfo = DataHelper::$examDefaultSeoInfo['syllabus-dropdown'] ?? [];
                $type = ucwords(strtolower(str_replace('-', ' ', $type)));
                if ($type == 'Logical Reasoning Data Interpretation') {
                    $type = 'Logical Reasoning & Data Interpretation';
                } elseif ($type == 'Commerce Accountancy') {
                    $type = 'Commerce & Accountancy';
                } elseif ($type == 'Political Science International Relations') {
                    $type = 'Political Science & International Relations';
                }
            }
        } else {
            $seoInfo = DataHelper::$examDefaultSeoInfo[$page] ?? [];
        }
        if (empty($seoInfo)) {
            return [];
        }
        foreach ($dates as $date) {
            if ($date->slug == 'exam-start') {
                $year = $year;
            }
        }
        $data = [];
        foreach ($seoInfo as $key => $value) {
            $data[$key] = strtr($value, [
                '{exam-name}' => $examName,
                '{year}' => $year,
                '{subject}' => $type
            ]);
        }
        return $data;
    }

    public static function calculateReadTime($content)
    {
        $word = str_word_count(self::htmlDecode($content, true));
        $min = (int) floor($word / 230);
        $sec = (int) floor($word % 230 / (230 / 60));

        return $min < 1 ? 1 . ' min' : $min . ' min';
        // return $min . ' min' . ($min == 1 ? '' : 's') . ', ' . $sec . ' sec' . ($sec == 1 ? '' : 's');
    }

    public static function getUserProfilePic(string $slug)
    {
        $userService = new UserService();
        $author = $userService->getUserBySlug($slug);

        return !empty($author['image']) ? Yii::getAlias('@profileDPFrontend') . '/' . $author['image'] : '/yas/images/usericon.png';
    }

    public static function getCollegeDefaultSeoInfo($college, $page, $menu = [], $infoDetails = [], $type = null)
    {
        $data = [];
        $feesName = '';
        $courseFees = '';
        $cutoffYear = '';
        $degree = '';
        $salary = '';
        $highlightsName = '';
        $seoInfo = DataHelper::$collegeDefaultSeoInfo[$page] ?? [];

        if (empty($seoInfo)) {
            return [];
        }

        if ($page == 'info') {
            $menus = array_keys(array_filter($menu, function ($value) {
                return !is_numeric($value);
            }));
            $infoCourse = ['info', 'courses-fees'];

            // Check if the array contains only "info" and "courses-fees"
            if (empty(array_diff($menus, $infoCourse)) && empty(array_diff($infoCourse, $menus))) {
                $seoInfo = $seoInfo['only_course_subpage'];
            } else {
                $seoInfo = $seoInfo['other_subpage'];
            }
        }

        // if ($page == 'program') {
        //     // Filter out null values
        //     $programDetails = array_filter($infoDetails, function ($value) {
        //         return $value !== null;
        //     });

        //     if (!empty($programDetails) && count($programDetails) == 1) {
        //         $seoInfo = $seoInfo['without_data'];
        //     } else {
        //         $seoInfo = $seoInfo['with_data'];
        //     }
        // }

        if (!empty($infoDetails)) {
            if (!empty($infoDetails['fees']) || !empty($infoDetails['fees-course-summary'])) {
                $feesData = '';
                if ($page == 'courses-fees' || $page == 'info') {
                    $feesData = !empty($infoDetails['fees']) ? $infoDetails['fees'] : [];
                    $columName = 'avgFees';
                }
                // elseif ($page == 'course') {
                //     $feesData = !empty($infoDetails['fees-course-summary']) ? $infoDetails['fees-course-summary'] : [];
                //     $columName = 'fees';
                // }
                if (!empty($feesData)) {
                    $feesArray = array_column($feesData, $columName);

                    $nonZeroValues = array_filter($feesArray, function ($value) {
                        return $value != 0;
                    });

                    if (!empty($nonZeroValues)) {
                        $feesName = 'Fees,';
                    } else {
                        $feesName = '';
                    }
                }

                // if ($page == 'program') {
                //     if (!empty($infoDetails['fees'])) {
                //         $feesName = 'Fees,';
                //     } else {
                //         $feesName = '';
                //     }
                // }
            }

            if ($page == 'courses-fees') {
                if (empty($feesName)) {
                    $seoInfo = $seoInfo['without-fees'];
                } else {
                    $seoInfo = $seoInfo['with-fees'];
                }
            }

            if ($page == 'placements') {
                if (!empty($infoDetails)) {
                    $highlightsName = array_column($infoDetails['salary'], 'name');

                    if (!empty($highlightsName) && (array_search('Average Salary Offered', $highlightsName) !== false) || array_search('Highest Salary Package Offered', $highlightsName) !== false) {
                        $seoInfo = $seoInfo['with-salary'];
                    } else {
                        $seoInfo = $seoInfo['without-salary'];
                    }

                    if (!empty($highlightsName)) {
                        if (array_search('Highest Salary Package Offered', $highlightsName) !== false && array_search('Average Salary Offered', $highlightsName) !== false) {
                            $salary = 'Highest & Average Salary Package,';
                        } else if (array_search('Average Salary Offered', $highlightsName) !== false) {
                            $salary = 'Average Salary Package,';
                        } elseif (array_search('Highest Salary Package Offered', $highlightsName) !== false) {
                            $salary = 'Highest Salary Package,';
                        }
                    }
                }
            }

            if ($page == 'cut-off') {
                //2023,2022,2021 || 2023,2022 || 2023,2021 || 2022,2021 || 2021 || 2022 || 2023
                if (!empty($infoDetails) && !empty($infoDetails['year'])) {
                    $seoInfo = $seoInfo['with-cut-off'];
                    if (isset($infoDetails['year'][2023]) && isset($infoDetails['year'][2022]) && isset($infoDetails['year'][2021])) {
                        $cutoffYear = 2023 . ',' . 2022 . ',' . 'and ' . 2021;
                    } elseif (isset($infoDetails['year'][2023]) && isset($infoDetails['year'][2022])) {
                        $cutoffYear = 2023 . ' and ' . 2022;
                    } elseif (isset($infoDetails['year'][2023]) && isset($infoDetails['year'][2021])) {
                        $cutoffYear = 2023 . ' and ' . 2021;
                    } elseif (isset($infoDetails['year'][2022]) && isset($infoDetails['year'][2021])) {
                        $cutoffYear = 2022 . ' and ' . 2021;
                    } elseif (isset($infoDetails['year'][2021])) {
                        $cutoffYear = 2021;
                    } elseif (isset($infoDetails['year'][2022])) {
                        $cutoffYear = 2022;
                    } elseif (isset($infoDetails['year'][2023])) {
                        $cutoffYear = 2023;
                    } else {
                        $cutoffYear = '';
                    }
                } else {
                    $seoInfo = $seoInfo['without-cut-off'];
                }
            }

            if ($page == 'facilities') {
                if (!empty($infoDetails['facilities'])) {
                    $facitilies = explode(', ', $infoDetails['facilities']);
                    $seoInfo = $seoInfo['with_facilities'];
                } else {
                    $seoInfo = $seoInfo['without_facilities'];
                }
            }
        }

        if (!empty($menu['courses-fees'])) {
            if (is_numeric($menu['courses-fees'])) {
                $courseFees = '';
            } else {
                $courseFees = 'Courses,';
            }
        }

        foreach ($seoInfo as $key => $value) {
            if (!empty($feesName)) {
                $feesName = $feesName;
                // if ($page == 'course') {
                //     $feesName = $key == 'description' ? 'courses & fees structure,' : 'Fees,';
                // } else {
                //     $feesName = $feesName;
                // }
            }
            // elseif (empty($feesName) && $page == 'course' && $key == 'description') {
            //     $feesName = 'courses,';
            // }

            $data[$key] = strtr($value, [
                '{college-name}' => $college,
                '{#admission_year}' => CollegeHelper::YEAR,
                '{ranking}' => !empty($menu['ranking']) ?  ($key == 'description' ? lcfirst($menu['ranking']) . ',' : $menu['ranking'] . ',') : '',
                '{courses-fees}' => !empty($courseFees) ? ($key == 'description' ? lcfirst($courseFees) : $courseFees) : '',
                '{fees}' => !empty($feesName) ? ($key == 'description' ? lcfirst($feesName) : $feesName) : '',
                '{contact-details}' => !empty($infoDetails) && isset($infoDetails['contact-details']) && in_array($infoDetails['contact-details'], $infoDetails) && $infoDetails['contact-details'] !== null ?  (($key == 'description') ? 'contact details,' : 'Contact Details,') : '',
                '{latest-updates}' => !empty($infoDetails) && isset($infoDetails['latest-updates']) && in_array($infoDetails['latest-updates'], $infoDetails) ? 'latest updates' : '',
                '{admission}' => !empty($menu) && isset($menu['admission']) && !is_numeric($menu['admission']) ? ($key == 'description' ? lcfirst($menu['admission']) . ',' : $menu['admission'] . ',') : '',
                '{placements}' => !empty($menu) && isset($menu['placements']) && !is_numeric($menu['placements']) ? ($key == 'description' ? lcfirst($menu['placements']) . ',' : ($key == 'title' && $page == 'program' ? $menu['placements'] . ',' :  $menu['placements'])) : '',
                '{salary_package}' =>  !empty($salary) ? (($key == 'description') ? strtolower($salary) : $salary) : '',
                '{companies}' => !empty($highlightsName) ? (array_search('Number of Participating Companies', $highlightsName) !== false ? (($key == 'description') ? 'top companies visiting' : 'Top Companies Visiting') : '') : '',
                '{facilities}' => !empty($menu) && !is_numeric($menu['facilities']) ? ($key == 'description' ? 'facilities' . ',' : 'Facilities' . ',') : '',
                '{cut-off}' => !empty($menu) && isset($menu['cut-off']) && !is_numeric($menu['cut-off']) ? ($page == 'admission' ? ($key == 'description' ? 'cutoff' : $menu['cut-off']) : ($key == 'description' ? 'cutoff' . ',' : $menu['cut-off'] . ',')) : '',
                '{course_count}' => $infoDetails['course-count'] ?? '',
                '{program_count}' => $infoDetails['program-count'] ?? '',
                '{courses_offered}' => !empty($infoDetails['courses-offered']) ? 'Courses Offered,' : '',
                '{entrance_exams}' => !empty($infoDetails['courses-offered']) ? ($key == 'description' ? 'entrance exams accepted,' : 'Entrance Exams,') : '',
                '{cutoff_year}' => empty($cutoffYear) ? '' : ($key !== 'description' ? str_replace(' and', ',', $cutoffYear) : $cutoffYear),
                '{campus}' => !empty($facitilies) && in_array('campus', $facitilies) ? ($key == 'description' ? 'campus,' : 'Campus,') : '',
                '{library}' => !empty($facitilies) && in_array('library', $facitilies) ? ($key == 'description' ?  'library,' : 'Library,') : '',
                '{hostel}' => !empty($facitilies) && in_array('hostel', $facitilies) ? ($key == 'description' ?  'hostel,' : 'Hostel,') : '',
                '{laboratory}' => !empty($facitilies) && in_array('laboratory', $facitilies) ? ($key == 'description' ?  'labs,' : 'Labs,') : '',
                '{gym}' => !empty($facitilies) && in_array('gym', $facitilies) ? ($key == 'description' ? 'gym,' : 'Gym,') : '',
                '{auditorium}' => !empty($facitilies) && in_array('auditorium', $facitilies) ? ($key == 'description' ? 'auditorium,' : 'Auditorium,') : '',
                '{cafeteria}' => !empty($facitilies) && in_array('cafeteria', $facitilies) ? ($key == 'description' ? 'cafeteria,'  : 'Cafeteria') : '',
                '{sports}' => !empty($facitilies) && in_array('sports', $facitilies) ? 'sports,' : '',
                '{transport}' => !empty($facitilies) && in_array('transpost', $facitilies) ? 'transpost,' : '',
                '{medical}' => !empty($facitilies) && in_array('medical', $facitilies) ? 'medical,' : '',
                '{wi-fi}' => !empty($facitilies) && in_array('wi-fi', $facitilies) ? 'wi-fi,' : '',
                '{#placement_year}' => CollegeHelper::PREVIOUS_YEAR,
                '{#result_year}' => CollegeHelper::PREVIOUS_YEAR,
                '{#cutoff_year}' => CollegeHelper::PREVIOUS_YEAR,
                '{city-name}' => $type,
                '{course-name}' => $type,
                '{program-name}' => $type,
                '{program-name}' => $type,
                '{placements_salary}' => !empty($infoDetails) && !empty($infoDetails['salary']) ? ($key == 'description' ? 'placement,'  : 'Placement,') : '',
                '{courses_fees_structure}' => !empty($infoDetails) && !empty($infoDetails['fees-course-summary']) ? 'Courses & Fees structure' : '',
                '{salary_packages}' => !empty($infoDetails) && !empty($infoDetails['salary']) ? 'highest & average salary packages,' : '',
                '{eligibility}' => !empty($infoDetails) && !empty($infoDetails['eligibility']) ? ($key == 'description' ? 'eligibility,'  : 'Eligibility,') : '',
                '{dates}' => !empty($infoDetails) && !empty($infoDetails['dates']) ? ($key == 'description' ? ' and Important Dates' : 'Dates') : ''

            ]);
        }

        $result = self::processSeoInfo($data, $page);

        if ($page == 'info' || $page == 'facilities') {
            $limit = $page == 'info' ? 4 : 5;
            $finalSeoInfo = self::showOnlyFewInfo($result, $limit, $page);
        } else {
            $finalSeoInfo = $result;
        }

        return $finalSeoInfo;
    }

    public function showOnlyFewInfo($values, $number, $page = '')
    {
        foreach ($values as $key => $value) {
            $semicolon_position = strpos($value, ':');
            $college_name = substr($value, 0, $semicolon_position);
            $sub_text = substr($value, $semicolon_position + 1);

            if ($semicolon_position !== false) {
                $strings = explode(',', $sub_text); // Split the subtext by comma and trim each string
                $trimmed_strings = array_map('trim', $strings);
                $maximun_string = array_slice($trimmed_strings, 0, $number);

                if ($key == 'description') {
                    $resultantData[$key] = $value;
                } else {
                    $resultantData[$key] = $college_name . ': ' . implode(', ', $maximun_string) . ($page == 'facilities' ? ' & Other Facilities' . (($key !== 'h1') ? '.' : '') : '');
                }
            } else {
                $resultantData[$key] = $value;
            }
        }

        return $resultantData;
    }

    public function processSeoInfo($input_array, $page = '')
    {
        foreach ($input_array as $key => $value) {
            //remove semi colon
            if (strpos($value, ':') !== false || $key === 'description') {
                $substring = substr($value, strpos($value, ':') + strlen(':'));

                if ($key == 'description') {
                    $value = preg_replace('/\s+/', ' ', $value);
                    $input_array[$key] = str_replace(' .', '.', trim($value));
                } else {
                    if (trim($substring) == '.') {
                        $input_array[$key] = rtrim(substr($value, 0, strpos($value, ':') + strlen(':')), ':') . (($key == 'title') ? '.' : '');
                    } else {
                        $value = preg_replace('/\s+/', ' ', $value);
                        $input_array[$key] = trim($value);
                    }
                }
            }

            //remove last comma, if there's no string
            if (strpos($value, ',') !== false) {
                $comma_index = strrpos($value, ',');
                $substring = substr($value, $comma_index + 1);

                if (trim($substring) === '' || trim($substring) === '.') {
                    $value = str_replace(' .', '.', (substr_replace($value, '', $comma_index, 1)));
                }

                $value = preg_replace('/\s+/', ' ', $value);
                $input_array[$key] = trim($value);
            }

            //remove "comma" and "like"
            // if ($key == 'description' && $page == 'facilities') {
            //     $description = $value;
            //     $output = $output = preg_replace('/like\s*(?=\band\b)/i', '', $description);
            //     $input_array[$key] = trim($output);
            // } else if ($page == 'facilities') {
            //     $data = $value;
            //     $output = preg_replace('/,\s*Other Facilities/', ' Other Facilities', $data);
            //     $input_array[$key] = trim($output);
            // }
        }

        if (!empty($input_array)) {
            $input_array['h1'] = !empty($input_array['h1']) ? str_replace('.', '', $input_array['h1']) : '';
            $input_array['title'] = str_replace('.', '', $input_array['title']);
        }

        return $input_array;
    }

    public static function getCollegeDefaultSubPageSeoInfo($college, $page, $type = null)
    {
        if ($type != null) {
            $tempPage = $page . '-dropdown';
            $seoInfo = DataHelper::$collegeDefaultSeoInfo[$tempPage] ?? [];
        } else {
            $seoInfo = DataHelper::$collegeDefaultSeoInfo[$page] ?? [];
        }
        $year = '';
        if (empty($seoInfo)) {
            return [];
        }

        $admissionYear = CollegeHelper::YEAR;

        $data = [];
        foreach ($seoInfo as $key => $value) {
            $data[$key] = strtr($value, [
                '{college-name}' => $college,
                '{#admission_year}' => $admissionYear,
                '{#placement_year}' => CollegeHelper::PREVIOUS_YEAR,
                '{#result_year}' => CollegeHelper::PREVIOUS_YEAR,
                '{#cutoff_year}' => CollegeHelper::PREVIOUS_YEAR,
                '{city-name}' => $type,
                '{course-name}' => $type,
                '{program-name}' => $type,

            ]);
        }
        return $data;
    }

    public static function parseAmpNewContent(string $content)
    {
        $data = [];
        $content = html_entity_decode($content);

        $content = preg_replace('#<(col|colgroup)([^>]*)\bwidth="[^"]*"([^>]*)>#i', '<$1$2$3>', $content);
        $content = preg_replace('#<table([^>]*)\bborder="[^"]*"([^>]*)>#i', '<table$1$2>', $content);
        $content = preg_replace('#<a([^>]*)\btarget="[^"]*"([^>]*)>#i', '<a$1$2>', $content);
        $content = preg_replace('#<h2([^>]*)\bname="[^"]*"([^>]*)>#i', '<h2$1$2>', $content);
        $content = preg_replace('#<td([^>]*)\bscope="[^"]*"([^>]*)>#i', '<td$1$2>', $content);
        $content = preg_replace('#<tr([^>]*)\baria-rowindex="[^"]*"([^>]*)>#i', '<tr$1$2>', $content);
        $content = preg_replace('#<tr([^>]*)\baria-rowcount="[^"]*"([^>]*)>#i', '<tr$1$2>', $content);
        $content = preg_replace('#<table([^>]*)\baria-rowindex="[^"]*"([^>]*)>#i', '<table$1$2>', $content);
        $content = preg_replace('#<table([^>]*)\baria-rowcount="[^"]*"([^>]*)>#i', '<table$1$2>', $content);
        $content = preg_replace('#<span([^>]*)\bdata-sheets-root="[^"]*"([^>]*)>#i', '<span$1$2>', $content);
        $content = preg_replace('#<span([^>]*)\bdata-sheets-userformat="[^"]*"([^>]*)>#i', '<span$1$2>', $content);
        $content = preg_replace('#<td([^>]*)\bdata-sheets-value="[^"]*"([^>]*)>#i', '<td$1$2>', $content);
        $content = preg_replace('#<td([^>]*)\bdata-sheets-hyperlink="[^"]*"([^>]*)>#i', '<td$1$2>', $content);
        $content = preg_replace('#<td([^>]*)\b1":2,"2":"nirf    ="[^"]*"([^>]*)>#i', '<td$1$2>', $content);
        $content = preg_replace('#<td([^>]*)\bdata-sheets-numberformat="[^"]*"([^>]*)>#i', '<td$1$2>', $content);
        $content = preg_replace('#<table([^>]*)\bdata-sheets-root="[^"]*"([^>]*)>#i', '<table$1$2>', $content);
        $content = preg_replace('#<span([^>]*)\bdata-sheets-value="[^"]*"([^>]*)>#i', '<span$1$2>', $content);
        $content = preg_replace('#<table([^>]*)\baria-described="[^"]*"([^>]*)>#i', '<table$1$2>', $content);

        $tweetId = preg_match_all('#<div class="tweet"[^>]*>(.*?)</div>#s', $content, $match);

        foreach ($match[0] as $key => $value) {
            $divTweets[] = $value;

            if (empty($divTweets)) {
                return $content;
            }

            foreach ($divTweets as $tweet) {
                $divTweet = html_entity_decode($tweet);
                $id = preg_match('/data-tweets="([^"]*)"/', $divTweet, $result);
                if (!empty($result[1])) {
                    $data[$divTweet] = '<amp-twitter width="375" height="472" layout="responsive" data-tweetid="' . $result[1] . '"></amp-twitter>';
                }
            }
        }

        $content = preg_replace_callback(
            '#<iframe([^>]*)\bsrc="([^"]+)"([^>]*)></iframe>#i',
            function ($matches) {
                $attributesBefore = $matches[1];
                $src = $matches[2];
                $attributesAfter = $matches[3];
                $attributesBefore = preg_replace('#\bwidth="[^"]*"#i', '', $attributesBefore);
                $attributesBefore = preg_replace('#\bheight="[^"]*"#i', '', $attributesBefore);
                $attributesAfter = preg_replace('#\bwidth="[^"]*"#i', '', $attributesAfter);
                $attributesAfter = preg_replace('#\bheight="[^"]*"#i', '', $attributesAfter);
                $ampHeight = '315';
                $ampWidth = '375';

                return '<amp-iframe layout="responsive" sandbox="allow-scripts allow-same-origin" width="' . $ampWidth . '" height="' . $ampHeight . '" src="' . $src . '"' . $attributesBefore . $attributesAfter . '></amp-iframe>';
            },
            $content
        );

        return strtr($content, $data);
    }



    //Indian currency formate
    public static function indMoneyFormat($number)
    {
        $decimal = (string)($number - floor($number));
        $money = floor($number);
        $length = strlen($money);
        $delimiter = '';
        $money = strrev($money);

        for ($i = 0; $i < $length; $i++) {
            if (($i == 3 || ($i > 3 && ($i - 1) % 2 == 0)) && $i != $length) {
                $delimiter .= ',';
            }
            $delimiter .= $money[$i];
        }

        $result = strrev($delimiter);
        $decimal = preg_replace('/0\./i', '.', $decimal);
        $decimal = substr($decimal, 0, 0);

        if ($decimal != '0') {
            $result = $result . $decimal;
        }

        return $result;
    }

    /*Parse Course Title*/
    public static function getParseCourseContent($meta_text)
    {
        $text = $meta_text;
        if (str_contains($text, '#admission_year')) {
            $text = str_replace('#admission_year', self::COURSE_ADMISSION_YEAR, $text);
        }
        return   $text;
    }

    /* College Page Date 6 months Calculate*/

    public static function getDateUpdateCollegePage($contentDate)
    {
        if (empty($contentDate)) {
            return false;
        }
        $contentDateStr =  strtotime(date('Y-m-d', strtotime($contentDate)));
        $sixMonthDateStr =  strtotime(date('Y-m-d', strtotime(Carbon::now()->subMonths(6))));
        if ($contentDateStr > $sixMonthDateStr) {
            return   Yii::$app->formatter->asDate($contentDate);
        } else {
            return false;
        }
    }

    public static function getGenerateHtml($content, $pageName, $downloadableResourceSection = '', $liveChatApplication = '')
    {
        $DOM = new DOMDocument('1.0', 'UTF-8');
        libxml_use_internal_errors(true);
        @$DOM->loadHTML(mb_convert_encoding($content, 'HTML-ENTITIES', 'UTF-8'));

        // $removeRelDomains = [
        //     'https://www.getmyuni.com/assets/images/',
        //     'https://www.getmyuni.com/',
        //     'http://www.getmyuni.com/',
        //     'https://www.getmyuni.com',
        //     'https://news.getmyuni.com/'
        // ];

        // foreach ($DOM->getElementsByTagName('a') as $anchor) {
        //     if ($anchor->hasAttribute('href') && $anchor->hasAttribute('rel')) {
        //         $href = $anchor->getAttribute('href');
        //         foreach ($removeRelDomains as $domain) {
        //             if (strpos($href, $domain) === 0) {
        //                 $anchor->removeAttribute('rel');
        //                 break;
        //             }
        //         }
        //     }
        // }

        $j = 1;
        $h2Text = [];
        $_tableOfContents = ['table-of-content', 'table-of-contents', 'table-of-contents:', 'visayasuci'];

        if (($DOM->getElementsByTagName('*'))->length > 10000) {
            $elements = $DOM->getElementsByTagName('p');
        } else {
            $elements = $DOM->getElementsByTagName('*');
        }
        foreach ($elements as $i => $element) {
            $textContent = '';
            if ($element->tagName == 'p') {
                $textContent = htmlentities($element->textContent, null, 'utf-8');
                $textContent = str_replace('&nbsp;', '', $textContent);
                $textContent_slug_title = explode(' ', $textContent);
                $textContent_slug_title = Inflector::slug(implode('-', $textContent_slug_title));
            }

            if ($element->tagName == 'p' && in_array($textContent_slug_title, $_tableOfContents)) {
                $ulElement2 = $DOM->getElementsByTagName('*')->item($i + 2);
                $ulElement3 = $DOM->getElementsByTagName('*')->item($i + 3);
                $ulElement2->parentNode->removeChild($ulElement2);
                $ulElement3->parentNode->removeChild($ulElement3);
                if ($element->parentNode != null) {
                    $element->parentNode->removeChild($element);
                }
            }
        }

        $h2Elements = $DOM->getElementsByTagName('h2');
        $h2Count = 0;
        $thirdH2 = null;

        foreach ($h2Elements as $element) {
            if (trim($element->textContent) != '') {
                if (in_array($pageName, self::$_collegePageContent)) {
                    $element->textContent =  str_replace('#admission_year', CollegeHelper::YEAR, $element->textContent);
                    $element->textContent =  str_replace('#placement_year', CollegeHelper::PREVIOUS_YEAR, $element->textContent);
                    $element->textContent =  str_replace('#result_year', CollegeHelper::PREVIOUS_YEAR, $element->textContent);
                    $element->textContent =  str_replace('#cutoff_year', CollegeHelper::PREVIOUS_YEAR, $element->textContent);
                } elseif (in_array($pageName, self::$_coursePageContent)) {
                    $element->textContent = str_replace('#admission_year', self::COURSE_ADMISSION_YEAR, $element->textContent);
                }
                $key = str_replace(' ', '-', $element->textContent);
                $string = htmlentities($key, null, 'utf-8');
                $content = str_replace('&nbsp;', '', $string);
                $key = html_entity_decode($content);
                $h2Text[$key] = $element->textContent;
                $element->setAttribute('id', strtolower($key));
                $element->setAttribute('class', 'scrollToh2CSS');
                $element->setAttribute('name', $key);
                $j++;
                $h2Count++;

                if ($h2Count === 3) {
                    $thirdH2 = $element;
                }
            }
        }

        if (!empty($downloadableResourceSection)) {
            $fragment = $DOM->createDocumentFragment();
            $fragment->appendXML($downloadableResourceSection);

            if ($thirdH2 !== null) {
                $thirdH2->parentNode->insertBefore($fragment, $thirdH2);
            } else {
                $body = $DOM->getElementsByTagName('body')->item(0);
                if ($body !== null) {
                    $body->appendChild($fragment);
                }
            }
        }

        if (!empty($liveChatApplication)) {
            $fragment = $DOM->createDocumentFragment();
            $fragment->appendXML($liveChatApplication);

            if ($thirdH2 !== null) {
                $thirdH2->parentNode->insertBefore($fragment, $thirdH2);
            } else {
                $body = $DOM->getElementsByTagName('body')->item(0);
                if ($body !== null) {
                    $body->appendChild($fragment);
                }
            }
        }

        $content = $DOM->saveHTML();
        $returnHtml['content'] = $content;
        $returnHtml['h2'] = $h2Text;
        return $returnHtml;
    }

    public static function getGenerateHtmlArticleAmp($content, $pageName, $slug = null)
    {
        $DOM = new DOMDocument('1.0', 'UTF-8');
        libxml_use_internal_errors(true);
        @$DOM->loadHTML(mb_convert_encoding($content, 'HTML-ENTITIES', 'UTF-8'));

        $removeRelDomains = [
            'https://www.getmyuni.com/assets/images/',
            'https://www.getmyuni.com/',
            'http://www.getmyuni.com/',
            'https://www.getmyuni.com',
            'https://news.getmyuni.com/'
        ];

        foreach ($DOM->getElementsByTagName('a') as $anchor) {
            if ($anchor->hasAttribute('href') && $anchor->hasAttribute('rel')) {
                $href = $anchor->getAttribute('href');
                foreach ($removeRelDomains as $domain) {
                    if (strpos($href, $domain) === 0) {
                        $anchor->removeAttribute('rel');
                        break;
                    }
                }
            }
        }

        $j = 1;
        $h2Text = [];
        $colText = [];
        $_tableOfContents = ['table-of-content', 'table-of-contents', 'table-of-contents:'];

        if (($DOM->getElementsByTagName('*'))->length > 10000) {
            $elements = $DOM->getElementsByTagName('p');
        } else {
            $elements = $DOM->getElementsByTagName('*');
        }

        foreach ($elements as $i => $element) {
            $textContent = '';
            if ($element->tagName == 'p') {
                $textContent = htmlentities($element->textContent, null, 'utf-8');
                $textContent = str_replace('&nbsp;', '', $textContent);
                $textContent_slug_title = explode(' ', $textContent);
                $textContent_slug_title = Inflector::slug(implode('-', $textContent_slug_title));
            }

            if ($element->tagName == 'p' && in_array($textContent_slug_title, $_tableOfContents)) {
                $ulElement2 = $DOM->getElementsByTagName('*')->item($i + 2);
                $ulElement3 = $DOM->getElementsByTagName('*')->item($i + 3);
                $ulElement2->parentNode->removeChild($ulElement2);
                $ulElement3->parentNode->removeChild($ulElement3);
                if ($element->parentNode != null) {
                    $element->parentNode->removeChild($element);
                }
            }
        }

        foreach ($DOM->getElementsByTagName('h2') as $element) {
            if (trim($element->textContent) != '') {
                if (in_array($pageName, self::$_collegePageContent)) {
                    $element->textContent =  str_replace('#admission_year', CollegeHelper::YEAR, $element->textContent);
                    $element->textContent =  str_replace('#placement_year', CollegeHelper::PREVIOUS_YEAR, $element->textContent);
                    $element->textContent =  str_replace('#result_year', CollegeHelper::PREVIOUS_YEAR, $element->textContent);
                    $element->textContent =  str_replace('#cutoff_year', CollegeHelper::PREVIOUS_YEAR, $element->textContent);
                } elseif (in_array($pageName, self::$_coursePageContent)) {
                    $element->textContent = str_replace('#admission_year', self::COURSE_ADMISSION_YEAR, $element->textContent);
                }
                $key = str_replace(' ', '-', $element->textContent);
                $string = htmlentities($key, null, 'utf-8');
                $content = str_replace('&nbsp;', '', $string);
                $key = html_entity_decode($content);
                $h2Text[$key] = $element->textContent;
                $element->setAttribute('id', strtolower($key));
                $element->setAttribute('class', 'scrollToh2CSS');
                //$element->setAttribute('name', $key);
                $j++;
            }
        }
        foreach ($DOM->getElementsByTagName('a') as $elementAnchor) {
            $articleLink = $elementAnchor->getAttribute('href');
            if (!empty($articleLink)) {
                if (str_contains($articleLink, '/articles/')) {
                    $link = str_replace('/articles/', 'amp/articles/', $articleLink);
                    $elementAnchor->setAttribute('href', $link);
                    $elementAnchor->removeAttribute('_target');
                    $elementAnchor->removeAttribute('rel');
                }
            }
        }

        foreach ($DOM->getElementsByTagName('col') as $key => $col) {
            $width_attr = $col->getAttribute('width');
            if (! empty($width_attr)) {
                $col->setAttribute('class', 'class-' . $width_attr);
                $col->removeAttribute('width');
                $colText[$key] = $width_attr;
            }
        }
        if ($slug !== 'nit-seat-matrix') {
            foreach ($DOM->getElementsByTagName('td') as $key => $col) {
                $col->removeAttribute('scope');
            }
        }

        $returnHtml['content'] = $DOM->saveHTML();
        $returnHtml['h2'] = $h2Text;
        $returnHtml['colText'] = $colText;
        return $returnHtml;
    }


    //exam widget functions starts
    public static function getEventBySlug($dates, $slug)
    {
        foreach ($dates as $event) {
            if ($event->slug === $slug) {
                return $event;
            }
        }
        return null;
    }

    public static function getEventStatus($start, $end, $today, &$showUpcoming, &$hasAnyDate)
    {
        $startDate = $start ? Carbon::parse($start) : null;
        $endDate = $end ? Carbon::parse($end) : null;

        $status = $liClass =  $iconClass = $statusHTML = '';
        $useSimpleFormat = false;

        if ($startDate || $endDate) {
            $hasAnyDate = true;
        }

        if (!$hasAnyDate) {
            $status = 'Coming Soon';
            $liClass = 'coming-soon-text';
            $statusHTML = '<span class="coming-soon" style="display: flex;">Coming Soon</span>';
            $useSimpleFormat = true;
            $showUpcoming = false;
        } elseif (!$startDate && !$endDate) {
            $status = 'Completed';
            $liClass = 'coming-step';
            $iconClass = 'tickicon';
            $showUpcoming = true;
        } elseif ($startDate) {
            $isStartToday = $startDate->eq($today);
            $isAfterToday = $startDate->gte($today);
            $isBeforeToday = $startDate->lte($today);

            if ($endDate) {
                if ($endDate->lte($today)) {
                    $status = 'Completed';
                    $liClass = 'coming-step';
                    $iconClass = 'tickicon';
                    $showUpcoming = true;
                } elseif ($isBeforeToday && $endDate->gte($today) && $showUpcoming) {
                    $status = 'Ongoing';
                    $liClass = 'coming-soon-text';
                    $statusHTML = '<span class="coming-soon" style="display: flex;">Ongoing</span>';
                    $useSimpleFormat = true;
                    $showUpcoming = false;
                } elseif ($isAfterToday && $showUpcoming) {
                    $status = 'Coming Soon';
                    $liClass = 'coming-soon-text';
                    $statusHTML = '<span class="coming-soon" style="display: flex;">Coming Soon</span>';
                    $useSimpleFormat = true;
                    $showUpcoming = false;
                }
            } else {
                if ($isBeforeToday) {
                    $status = 'Completed';
                    $liClass = 'coming-step';
                    $iconClass = 'tickicon';
                    $showUpcoming = true;
                } elseif (($isStartToday || $isAfterToday) && $showUpcoming) {
                    $status = $isStartToday ? 'Ongoing' : 'Coming Soon';
                    $liClass = 'coming-soon-text';
                    $statusHTML = '<span class="coming-soon" style="display: flex;">' . $status . '</span>';
                    $useSimpleFormat = true;
                    $showUpcoming = false;
                }
            }
        }

        return [
            'status' => $status,
            'liClass' => $liClass,
            'iconClass' => $iconClass,
            'statusHTML' => $statusHTML,
            'useSimpleFormat' => $useSimpleFormat,
            'startDate' => $startDate,
            'endDate' => $endDate,
        ];
    }

    //used to execute the CTA
    public function isEventOngoing($startDate, $endDate, $currentDate)
    {
        $startDate = Carbon::parse($startDate);
        $currentDate = Carbon::parse($currentDate);

        if (!is_null($endDate)) {
            $endDate = Carbon::parse($endDate);
            return $currentDate->between($startDate, $endDate, true);
        }

        // If end is null, show CTA only if start is today
        return $startDate->isSameDay($currentDate);
    }

    public static function renderEventsHtml($registrationEvent, $admitCardEvent, $examDayEvent, $answerKeyEvent, $resultsOutEvent, $entity, $entity_id, $slug, $name)
    {
        $currentDate = Carbon::now();

        if ($registrationEvent && self::isEventOngoing($registrationEvent->start, $registrationEvent->end, $currentDate)) {
            return self::renderCtaBlock('registration', [
                'left' => 'Previous Year Question Papers {download}',
                'right' => 'Sample Papers {download}',
                'alternateRight' => 'Help Me Apply',
                'alternateLeft' => 'Check Eligibility',
            ], $slug, $entity, $entity_id, $name);
        }
        if ($admitCardEvent && self::isEventOngoing($admitCardEvent->start, $admitCardEvent->end, $currentDate)) {
            // dd($admitCardEvent, self::isEventOngoing($admitCardEvent->start, $admitCardEvent->end, $currentDate));
            return self::renderCtaBlock('admit-card', [
                'left' => 'Sample Papers {download}',
                'right' => 'Admit Card {download}',
                'alternateLeft' => 'Check Eligibility',
            ], $slug, $entity, $entity_id, $name);
        }

        if ($examDayEvent && self::isEventOngoing($examDayEvent->start, $examDayEvent->end, $currentDate)) {
            return self::renderCtaBlock('exam-start', [
                'left' => 'Paper Analysis {download}',
                'right' => 'Answer Key {download}',
                'alternateRight' => 'Check Eligibility',
            ], $slug, $entity, $entity_id, $name);
        }

        if ($answerKeyEvent && self::isEventOngoing($answerKeyEvent->start, $answerKeyEvent->end, $currentDate)) {
            return self::renderCtaBlock('answer-key', [
                'left' => 'Response Sheet {download}',
                'right' => 'Answer Key {download}',
                'alternateRight' => 'Check Eligibility',
                'alternateLeft' => 'Check Eligibility',
            ], $slug, $entity, $entity_id, $name);
        }

        if ($resultsOutEvent && self::isEventOngoing($resultsOutEvent->start, $resultsOutEvent->end, $currentDate)) {
            return self::renderCtaBlock('result-date', [
                'left' => 'Get {name} Alerts',
                'right' => 'Download Cut Off {download}',
                'alternateRight' => 'Apply Now {list}',
            ], $slug, $entity, $entity_id, $name);
        }

        // Fallback CTA Logic
        if ($resultsOutEvent && Carbon::parse($resultsOutEvent->end)->diffInDays($currentDate, false) <= 90) {
            // Result ended within 90 days
            return self::renderCtaBlock('default', [
                'left' => 'Register Now',
                'right' => 'Get Free Counselling',
            ], $slug, $entity, $entity_id, $name);
        } else {
            // Result older than 90 days or not available
            return self::renderCtaBlock('default', [
                'right' => 'Previous Year Question Papers {download}',
                'left' => 'College Predictor',
                'alternateRight' => 'Get Free Counselling',
                'alternateLeft' => 'Register Now'
            ], $slug, $entity, $entity_id, $name);
        }
    }

    public static function renderCtaBlock($eventKey, $ctaTemplates, $slug, $entity, $entity_id, $name)
    {
        $ctaLocationPrefix = 'exam_' . $slug . '_' . $eventKey;

        if (!is_array($ctaTemplates)) {
            $ctaTemplates = [
                'left' => $ctaTemplates,
                'right' => '',
            ];
        }

        $finalCtas = [];

        foreach (['left', 'right'] as $side) {
            $ctaTemplate = isset($ctaTemplates[$side]) ? $ctaTemplates[$side] : '';
            $ctaTextFinal = UserService::cleanText($ctaTemplate, DataHelper::$ctaOtherCategoryarray[5]);

            // Get related media file
            $mediaFile = MediaDriveUploadType::find()
                ->alias('mdu')
                ->select(['md.entity_id', 'md.page', 'md.sub_page'])
                ->leftJoin('media_drive md', 'md.sub_page = mdu.id')
                ->where([
                    'mdu.entity' => DataHelper::$ctaOtherCategoryarray[5]['entity'],
                    'md.entity_id' => $entity_id,
                    'md.status' => MediaDrive::STATUS_ACTIVE
                ])
                ->andWhere(new \yii\db\Expression("upload_type REGEXP '" . preg_quote($ctaTextFinal['ctaTextFinal'], '/') . "?'"))
                ->asArray()
                ->all();

            $column = DataHelper::$ctaEntityFieldsArr[DataHelper::$ctaOtherCategoryarray[5]['entity']];

            $result = [];

            //change to dynamic if needed
            if (!empty($column)) {
                $result = ExamContent::find()->select(['id', 'name', 'exam_id'])
                    ->where(['name' => $ctaTextFinal['ctaTextFinal']])
                    ->andWhere(['exam_id' => $entity_id])
                    ->andWhere(['status' => 1])
                    ->one();
            }

            // If there's no regular CTA (empty media and result), use the alternate CTA (if available)
            if (empty($mediaFile) && empty($result)) {
                $ctaTemplate = $ctaTemplates['alternate' . ucfirst($side)] ?? $ctaTemplates[$side];
            }

            $alternateMedia = !$mediaFile && !empty($ctaTextFinal['ctaTextFinal']) ? [
                'media_value' => (!empty($result) && $ctaTextFinal['subpageValue'] !== '' && strpos($ctaTemplate, 'Predict') !== false) ? 'page_redirect' : 'page_download',
                'page_redirect_slug' => Url::base(true) . '/' . DataHelper::$ctaOtherCategoryarray[5]['entityRedirect'] . '/' . $slug . '-' . Inflector::slug($ctaTextFinal['ctaTextFinal']),
            ] : [];

            $finalCtas[$side] = Html::leadButton(
                UserService::parseDynamicCta($ctaTemplate, $name, $slug),
                [
                    'entity' => $entity,
                    'entityId' => $entity_id,
                    'ctaLocation' => $ctaLocationPrefix . '_' . $side . ' _exam_widget_cta',
                    'ctaText' => UserService::parseDynamicCta($ctaTemplate, $name, $slug),
                    'ctaTitle' => 'Register to Get Exam Alerts',
                    'leadformtitle' => 'REGISTER TO GET EXAM ALERTS',
                    'subheadingtext' => 'REGISTER TO GET EXAM ALERTS',
                    'image' => 'https://media.getmyuni.com/yas/images/defaultcardbanner.png',
                    'alternateMedia' => $alternateMedia['media_value'] ?? '',
                    'alternatePageRedirectSlug' => isset($alternateMedia['page_redirect_slug']) ? $alternateMedia['page_redirect_slug'] : ''
                ],
                ['class' => $side == 'left' ? 'examSecondrayPrimary primaryBtn applyNowButton getLeadForm examLeadValue' : 'examSecondray applyNowButton getLeadForm examLeadValue']
            );
        }

        return '<div class="glance-options-button">' . $finalCtas['left'] . $finalCtas['right'] . '</div>';
    }

    public static function formatDateWithSuffix(\DateTime $date)
    {
        $day = (int) $date->format('j'); // Day without leading zero
        $suffix = 'th';

        if (!in_array(($day % 100), [11, 12, 13])) {
            switch ($day % 10) {
                case 1:
                    $suffix = 'st';
                    break;
                case 2:
                    $suffix = 'nd';
                    break;
                case 3:
                    $suffix = 'rd';
                    break;
            }
        }

        return $day . $suffix . ' ' . $date->format('M Y');
    }



    protected static function loadDom(string $html): array
    {
        libxml_use_internal_errors(true);
        $doc = new \DOMDocument();
        $doc->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
        libxml_clear_errors();
        return [$doc, new \DOMXPath($doc)];
    }

    /**
     * Extract inner HTML of body tag.
     */
    protected static function getBodyInnerHtml(\DOMDocument $doc): string
    {
        $body = $doc->getElementsByTagName('body')->item(0);
        $html = '';
        if ($body) {
            foreach ($body->childNodes as $child) {
                $html .= $doc->saveHTML($child);
            }
        }
        return $html;
    }

    /**
     * Remove <p> containing a phrase and preceding <h3> (skipping empty <p> and text nodes).
     */
    protected static function removeParagraphsAndHeadingsWithPhrase(\DOMXPath $xpath, string $phrase): void
    {
        $paragraphs = $xpath->query('//p[contains(., "' . $phrase . '")]');

        foreach ($paragraphs as $p) {
            if (!$p || !$p->parentNode) {
                continue;
            }

            // Save previous sibling before removal
            $prev = $p->previousSibling;

            // Remove target paragraph
            $p->parentNode->removeChild($p);

            // Traverse backwards to remove empty <p> and then <h3>
            while ($prev) {
                if ($prev->nodeType === XML_TEXT_NODE && trim($prev->textContent) === '') {
                    $prev = $prev->previousSibling;
                    continue;
                }

                if ($prev->nodeName === 'p') {
                    $text = trim($prev->textContent);
                    $textClean = str_replace("\xC2\xA0", '', $text); // UTF-8 &nbsp;
                    if ($textClean === '' || $textClean === '&nbsp;') {
                        $temp = $prev->previousSibling;
                        if ($prev->parentNode) {
                            $prev->parentNode->removeChild($prev);
                        }
                        $prev = $temp;
                        continue;
                    }
                }

                if ($prev->nodeName === 'h3') {
                    if ($prev->parentNode) {
                        $prev->parentNode->removeChild($prev);
                    }
                    break;
                }

                break; // stop if no relevant nodes found
            }
        }
    }

    /**
     * Remove <tr> rows that are empty or contain only placeholders / 'Not Available'.
     */
    public static function removeEmptyPlaceholderRows(string $htmlContent): string
    {
        [$doc, $xpath] = self::loadDom($htmlContent);

        $rows = $xpath->query('//tr');

        foreach ($rows as $row) {
            $removeRow = true;
            foreach ($row->getElementsByTagName('td') as $cell) {
                $cellText = trim($cell->textContent);

                if ($cellText !== '' &&
                    !preg_match('/\{College_NIRF_Name_\d+\}|\{NIRF_Rank_\d+\}/i', $cellText) &&
                    strcasecmp($cellText, 'Not Available') !== 0
                ) {
                    $removeRow = false;
                    break;
                }
            }
            if ($removeRow && $row->parentNode) {
                $row->parentNode->removeChild($row);
            }
        }

        return self::getBodyInnerHtml($doc);
    }

    /**
     * Remove empty table sections and paragraphs with specific phrases.
     */
    public static function removeEmptyTableSections(string $html): string
    {
        [$doc, $xpath] = self::loadDom($html);

        // Remove paragraphs containing the phrase + preceding headings
        self::removeParagraphsAndHeadingsWithPhrase($xpath, 'with the fees ranging from INR Not Available.');

        // Remove tables with no data rows and related elements
        $tables = $xpath->query('//table');

        foreach ($tables as $table) {
            $rows = $table->getElementsByTagName('tr');
            $dataRowCount = max(0, $rows->length - 1);

            if ($dataRowCount === 0) {
                $nodesToRemove = [$table];

                $parent = $table->parentNode;
                if ($parent && strtolower($parent->nodeName) === 'div') {
                    $nodesToRemove[] = $parent;
                }

                // Remove one preceding <p> if any
                $prev = $parent ? $parent->previousSibling : $table->previousSibling;
                while ($prev && $prev->nodeType === XML_TEXT_NODE && trim($prev->textContent) === '') {
                    $prev = $prev->previousSibling;
                }

                if ($prev && $prev->nodeType === XML_ELEMENT_NODE && strtolower($prev->nodeName) === 'p') {
                    $nodesToRemove[] = $prev;

                    // Check one more preceding <h3>
                    $prev = $prev->previousSibling;
                    while ($prev && $prev->nodeType === XML_TEXT_NODE && trim($prev->textContent) === '') {
                        $prev = $prev->previousSibling;
                    }
                    if ($prev && $prev->nodeType === XML_ELEMENT_NODE && strtolower($prev->nodeName) === 'h3') {
                        $nodesToRemove[] = $prev;
                    }
                }

                // Remove all collected nodes safely
                foreach ($nodesToRemove as $node) {
                    if ($node && $node->parentNode) {
                        $node->parentNode->removeChild($node);
                    }
                }
            }
        }

        return self::getBodyInnerHtml($doc);
    }
}
